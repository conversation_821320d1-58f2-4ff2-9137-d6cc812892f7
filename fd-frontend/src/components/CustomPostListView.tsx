'use client';

import React, { useState, useCallback, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { CustomTypePost } from './CustomTypeViews/BaseTypeView';
import { RoutePrefixes } from '@/types/routes';
import { useCustomPosts } from '@/hooks/useCustomPosts';
import InfiniteScroll from './InfiniteScroll';
import { useSearchParams, useRouter } from 'next/navigation';

// 动态导入视图组件
const NotesView = dynamic(() => import('@/components/CustomTypeViews/NotesView'));
const BooksView = dynamic(() => import('@/components/CustomTypeViews/BooksView'));
const SoftwareView = dynamic(() => import('@/components/CustomTypeViews/SoftwareView'));
const ProductsView = dynamic(() => import('@/components/CustomTypeViews/ProductsView'));
const DefaultView = dynamic(() => import('@/components/CustomTypeViews/DefaultView'));

// 导入骨架屏组件
const NotesViewSkeleton = dynamic(() => import('@/components/CustomTypeViews/NotesViewSkeleton'));
// ... 可以在此为其他视图导入骨架屏 ...

interface PageInfo {
  hasNextPage: boolean;
  endCursor: string | null;
}

interface CustomPostListViewProps {
  initialPosts: CustomTypePost[];
  initialPageInfo: PageInfo;
  postType: string;
  query: string;
  viewComponent: string;
  routePrefixes: RoutePrefixes;
  categories?: { id: string; name: string; slug: string }[];
}

/**
 * 自定义类型列表的核心视图
 * 负责渲染正确的视图组件和处理客户端交互
 */
const CustomPostListView: React.FC<CustomPostListViewProps> = ({
  initialPosts,
  initialPageInfo,
  postType,
  query,
  viewComponent,
  routePrefixes,
  categories,
}) => {
  console.log(`%c[CPL Render] Component rendered. postType: ${postType}, initialPosts count: ${initialPosts.length}`, 'color: blue; font-weight: bold;');

  const router = useRouter();
  const searchParams = useSearchParams();
  const initCat = searchParams.get('cat') || '';
  const [activeCategory, setActiveCategory] = useState<string>(initCat);
  console.log(`[CPL State] activeCategory is: "${activeCategory}"`);

  // 当分类变化时，更新URL
  useEffect(() => {
    console.log(`[CPL Effect] activeCategory changed to "${activeCategory}". Updating URL.`);
    const params = new URLSearchParams(searchParams.toString());
    if (activeCategory) {
      params.set('cat', activeCategory);
    } else {
      params.delete('cat');
    }
    // 使用router.replace来更新URL而无需重新加载页面
    // 注意：这里的URL变化是为了保持UI和URL同步，它本身不触发数据重新获取
    // 数据的重新获取是由useCustomPosts hook内部逻辑处理的
    router.replace(`?${params.toString()}`, { scroll: false });
  }, [activeCategory, router, searchParams]);

  // 汲取 activeCategory 作为变量传给 useCustomPosts
  const { posts: apolloPosts, pageInfo, loading, error, loadMore, refetch } = useCustomPosts({
    postType,
    query,
    categorySlug: activeCategory || undefined,
  });
  console.log(`%c[CPL Hook] useCustomPosts returned: loading=${loading}, apolloPosts count: ${apolloPosts?.length || 0}`, 'color: green;');

  const [loadingMore, setLoadingMore] = useState(false);

  // 当有分类筛选时，我们完全依赖 aolloPosts。
  // 仅在“全部”这个初始状态下，如果 apolloPosts 为空（例如首次加载），我们才使用 initialPosts。
  const displayPosts = activeCategory
    ? apolloPosts
    : (apolloPosts && apolloPosts.length > 0 ? apolloPosts : initialPosts);
  console.log(`%c[CPL Logic] Final displayPosts count: ${displayPosts?.length || 0}. Source: ${activeCategory ? 'apolloPosts' : (apolloPosts && apolloPosts.length > 0 ? 'apolloPosts' : 'initialPosts')}`, 'color: orange; font-weight: bold;');

  // 决定分页状态：
  // 同样，优先使用客户端获取的分页状态，但如果它不存在，则回退到服务器的初始状态。
  const displayPageInfo = pageInfo || initialPageInfo;

  console.log(`%c[CPL PageInfo] displayPageInfo:`, 'color: purple;', {
    hasNextPage: displayPageInfo?.hasNextPage,
    endCursor: displayPageInfo?.endCursor,
    source: pageInfo ? 'apollo' : 'initial',
    activeCategory
  });

  const handleLoadMore = useCallback(async () => {
    if (loading || loadingMore || !displayPageInfo?.hasNextPage) {
      return;
    }

    setLoadingMore(true);
    try {
      if (displayPageInfo.endCursor) {
        await loadMore(displayPageInfo.endCursor);
      }
    } catch (err) {
      console.error("Failed to load more content:", err);
    } finally {
      setLoadingMore(false);
    }
  }, [loading, loadingMore, displayPageInfo, loadMore]);
  
  const renderView = (postsToRender: CustomTypePost[]) => {
    console.log(`[CPL RenderView] Rendering view component with ${postsToRender.length} posts.`);
    switch (viewComponent) {
      case 'notes':
        return <NotesView posts={postsToRender} type={postType} routePrefixes={routePrefixes} query={query} />;
      case 'books':
        return <BooksView posts={postsToRender} type={postType} routePrefixes={routePrefixes} query={query} />;
      case 'software':
        return <SoftwareView posts={postsToRender} type={postType} routePrefixes={routePrefixes} query={query} />;
      case 'products':
        return <ProductsView posts={postsToRender} type={postType} routePrefixes={routePrefixes} query={query} />;
      default:
        return <DefaultView posts={postsToRender} type={postType} routePrefixes={routePrefixes} query={query} />;
    }
  };

  const renderSkeleton = () => {
    switch(viewComponent) {
      case 'notes':
        return <NotesViewSkeleton count={8} />;
      // ... other cases
      default:
        return <p>加载中...</p>; // 默认骨架屏
    }
  };
  
  return (
    <>
      {/* 分类筛选条 */}
      {categories && categories.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            className={`px-3 py-1 rounded-full text-sm border ${activeCategory === '' ? 'bg-indigo-600 text-white' : 'text-gray-700'}`}
            onClick={() => {
              console.log('%c[CPL Click] Clicked "全部" button.', 'color: red;');
              setActiveCategory('');
            }}
          >全部</button>
          {categories.map(cat => (
            <button
              key={cat.slug}
              className={`px-3 py-1 rounded-full text-sm border ${activeCategory === cat.slug ? 'bg-indigo-600 text-white' : 'text-gray-700'}`}
              onClick={() => {
                console.log(`%c[CPL Click] Clicked category button: "${cat.slug}"`, 'color: red;');
                setActiveCategory(cat.slug);
              }}
            >{cat.name}</button>
          ))}
        </div>
      )}
      <InfiniteScroll
        hasMore={!!displayPageInfo?.hasNextPage}
        loading={loadingMore}
        onLoadMore={handleLoadMore}
        loadingComponent={
          <div className="text-center py-4" key={0}>
            <p>正在加载更多...</p>
          </div>
        }
      >
        {loading && (!displayPosts || displayPosts.length === 0)
          ? renderSkeleton()
          : renderView(displayPosts as CustomTypePost[])
        }
      </InfiniteScroll>
      {!displayPageInfo?.hasNextPage && (
        <div className="text-center py-4 text-gray-500">
          <p>已经到底部了</p>
        </div>
      )}
    </>
  );
};

export default CustomPostListView; 