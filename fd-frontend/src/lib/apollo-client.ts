// @ts-nocheck
import { ApolloClient, InMemoryCache, createHttpLink, from, ApolloLink, gql } from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import { setContext } from '@apollo/client/link/context';
import { getAuthToken, setAuthToken, clearAuthData } from '../utils/auth-utils';
import { Observable } from '@apollo/client/utilities';

// 定义节点接口，用于类型注解
interface Node {
  id: string;
  [key: string]: any; // 允许其他属性
}

// GraphQL API的HTTP链接
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost/graphql',
});

// 刷新令牌变更
const REFRESH_AUTH_TOKEN = gql`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`;

// 存储当前刷新令牌操作的Promise，避免多次并发刷新
let refreshTokenPromise: Promise<string | null> | null = null;

// 刷新认证令牌的函数
const refreshToken = async (client: ApolloClient<any>): Promise<string | null> => {
  if (refreshTokenPromise) {
    return refreshTokenPromise;
  }

  // 从cookie或localStorage获取refreshToken
  const refreshToken = localStorage.getItem('fd_refresh_token');
  
  if (!refreshToken) {
    // 如果没有刷新令牌，清除auth数据并返回null
    clearAuthData();
    return null;
  }
  
  refreshTokenPromise = new Promise<string | null>(async (resolve) => {
    try {
      const refreshResult = await client.mutate({
        mutation: REFRESH_AUTH_TOKEN,
        variables: {
          input: {
            jwtRefreshToken: refreshToken,
          },
        },
      });
      
      const newToken = refreshResult?.data?.refreshJwtAuthToken?.authToken;
      
      if (newToken) {
        // 保存新的token
        setAuthToken(newToken);

        // 同步到 HTTP-Only Cookie 供 SSR 使用
        try {
          await fetch('/api/auth/set-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ authToken: newToken, refreshToken }),
          });
        } catch (e) {
          console.error('刷新令牌后写 Cookie 失败', e);
        }

        resolve(newToken);
      } else {
        clearAuthData();
        resolve(null);
      }
    } catch (error) {
      console.error('刷新令牌失败:', error);
      clearAuthData();
      resolve(null);
    } finally {
      refreshTokenPromise = null;
    }
  });
  
  return refreshTokenPromise;
};

// 认证链接，用于添加JWT令牌到请求头
const authLink = setContext((_, { headers }) => {
  // 从localStorage获取令牌（仅在客户端）
  const token = getAuthToken();
  
  // 返回带有headers的上下文
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  };
});

// 错误处理链接
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  // 创建一个全局变量来存储上一个GraphQL错误信息
  if (typeof window !== 'undefined') {
    window.__LAST_GRAPHQL_ERROR__ = graphQLErrors?.[0] || null;
  }

  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      const { message, extensions } = err;
      
      // 处理认证错误，如过期的token
      if (
        extensions?.code === 'UNAUTHENTICATED' || 
        /expired token/i.test(message) ||
        /requires you to be logged in/i.test(message)
      ) {
        // 避免刷新令牌操作本身进入无限循环
        if (operation.operationName === 'RefreshAuthToken') {
          // 刷新令牌操作失败，清除认证数据
          if (typeof window !== 'undefined') {
            clearAuthData();
          }
          continue;
        }
        
        // 返回一个新的Observable，在刷新令牌后重试操作
        return new Observable(observer => {
          // 使用Apollo Client进行刷新，以便使用缓存和其他设置
          refreshToken(operation.getContext().client)
            .then(newToken => {
              if (!newToken) {
                // 如果无法获取新token，则传递错误
                observer.error(err);
                observer.complete();
                return;
              }
              
              // 使用新的token重试原始操作
              const subscriber = {
                next: observer.next.bind(observer),
                error: observer.error.bind(observer),
                complete: observer.complete.bind(observer),
              };
              
              // 克隆原始操作
              const originalContext = operation.getContext();
              const headers = {
                ...originalContext.headers,
                authorization: `Bearer ${newToken}`,
              };
              
              // 设置新的验证头部
              operation.setContext({
                ...originalContext,
                headers,
              });
              
              // 重新执行查询
              forward(operation).subscribe(subscriber);
            })
            .catch(refreshError => {
              observer.error(refreshError);
              observer.complete();
            });
        });
      }
    }

    // 修改错误处理，确保原始GraphQL错误信息被保留和传递
    if (graphQLErrors[0]) {
      const originalError = graphQLErrors[0];
      // 创建一个带有GraphQL错误标记的Error对象
      const enhancedError = new Error(originalError.message);
      // 添加标记和原始数据
      Object.defineProperty(enhancedError, 'isGraphQLError', { value: true });
      Object.defineProperty(enhancedError, 'originalGraphQLError', { value: originalError });
      Object.defineProperty(enhancedError, 'extensions', { value: originalError.extensions });
      
      // 存储到全局变量，方便其他地方访问
      if (typeof window !== 'undefined') {
        window.__ENHANCED_GRAPHQL_ERROR__ = enhancedError;
      }
    }
  }

  if (networkError) {
    // 创建增强的网络错误
    if (typeof window !== 'undefined') {
      const enhancedNetworkError = new Error(`网络错误: ${networkError.message}`);
      Object.defineProperty(enhancedNetworkError, 'isNetworkError', { value: true });
      Object.defineProperty(enhancedNetworkError, 'originalNetworkError', { value: networkError });
      window.__ENHANCED_NETWORK_ERROR__ = enhancedNetworkError;
    }
  }

  // 继续执行请求
  return forward(operation);
});

// 日志链接，仅在开发环境下使用
const loggerLink = new ApolloLink((operation, forward) => {
  // 移除开发环境日志记录
  return forward(operation).map((response) => {
    // 移除开发环境响应日志记录
    return response;
  });
});

// 缓存配置
const cache = new InMemoryCache({
  typePolicies: {
    // 针对分页数据的合并策略
    Query: {
      fields: {
        posts: {
          // 更具体的keyArgs可以更好地区分不同查询
          keyArgs: ['where', ['categoryId', 'tagId', 'search', 'author']],
          merge(existing = { nodes: [] }, incoming, { args }) {
            // 如果是新查询或显式替换，直接返回incoming
            if (!existing || !incoming) return incoming;
            
            // 判断是否是分页加载
            const isPagination = args?.after && incoming.pageInfo?.hasNextPage !== undefined;
            
            // 如果不是分页加载，直接返回新数据（替换旧数据）
            if (!isPagination) {
              return incoming;
            }
            
            // 分页加载时，合并且去重
            const existingIds = new Set(existing.nodes.map((node: Node) => node.id));
            const uniqueNewNodes = incoming.nodes.filter((node: Node) => !existingIds.has(node.id));
            
            return {
              ...incoming,
              nodes: [...existing.nodes, ...uniqueNewNodes],
            };
          },
        },
        contentNodes: {
          // 精确的缓存键：包含分类筛选信息，确保不同分类有独立缓存
          keyArgs: ['where', ['contentTypes', 'taxQuery']],
          merge(existing = { nodes: [] }, incoming, { args }) {
            if (!incoming) return existing;
            if (!existing) return incoming;

            // 判断是否是分页加载：
            // 1. 有after参数 AND
            // 2. 现有数据不为空 AND
            // 3. incoming数据的pageInfo表明这是分页请求
            const isPagination = !!(
              args?.after &&
              existing.nodes.length > 0 &&
              incoming.pageInfo
            );

            // 如果不是分页加载，直接返回新数据（替换旧数据）
            // 这包括：首次加载、分类切换等场景
            if (!isPagination) {
              return incoming;
            }

            // 分页加载时，合并且去重
            const existingIds = new Set(existing.nodes.map((node: Node) => node.id));
            const uniqueNewNodes = incoming.nodes.filter((node: Node) => !existingIds.has(node.id));

            return {
              ...incoming,
              nodes: [...existing.nodes, ...uniqueNewNodes],
            };
          },
        },
        products: {
          keyArgs: ['where'],
          merge(existing = { nodes: [] }, incoming, { args }) {
            if (!existing || !incoming) return incoming;
            
            // 判断是否是分页加载
            const isPagination = args?.after && incoming.pageInfo?.hasNextPage !== undefined;
            
            // 如果不是分页加载，直接返回新数据
            if (!isPagination) {
              return incoming;
            }
            
            // 分页加载时，合并且去重
            const existingIds = new Set(existing.nodes.map((node: Node) => node.id));
            const uniqueNewNodes = incoming.nodes.filter((node: Node) => !existingIds.has(node.id));
            
            return {
              ...incoming,
              nodes: [...existing.nodes, ...uniqueNewNodes],
            };
          },
        },
        categories: {
          keyArgs: false,
        },
        comments: {
          keyArgs: ['where.contentId'],
          merge(existing = { nodes: [] }, incoming, { args }) {
            if (!existing || !incoming) return incoming;
            
            // 判断是否是分页加载
            const isPagination = args?.after && incoming.pageInfo?.hasNextPage !== undefined;
            
            // 如果不是分页加载，直接返回新数据
            if (!isPagination) {
              return incoming;
            }
            
            // 分页加载时，合并且去重
            const existingIds = new Set(existing.nodes.map((node: Node) => node.id));
            const uniqueNewNodes = incoming.nodes.filter((node: Node) => !existingIds.has(node.id));
            
            return {
              ...incoming,
              nodes: [...existing.nodes, ...uniqueNewNodes],
            };
          },
        },
        // 修复 contentNode 缓存合并问题
        contentNode: {
          keyArgs: ['id', 'idType'],
          merge(existing, incoming, { args, mergeObjects }) {
            // 如果没有现有数据，直接返回新数据
            if (!existing) return incoming;
            if (!incoming) return existing;

            // 安全合并对象，保留所有字段
            return {
              ...existing,
              ...incoming,
              // 特殊处理嵌套对象
              ...(existing.__typename === incoming.__typename && {
                __typename: incoming.__typename
              })
            };
          },
        },
        // 其他自定义缓存策略...
      },
    },
    // 移除类型策略，让 Apollo 使用默认的 __typename + id 组合
  },
});

// 创建Apollo Client实例
const client = new ApolloClient({
  link: from([errorLink, loggerLink, authLink, httpLink]),
  cache,
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network', // 首先使用缓存数据，同时发送网络请求更新缓存
      errorPolicy: 'all', // 返回错误和数据
      notifyOnNetworkStatusChange: true, // 允许UI响应加载状态变化
    },
    query: {
      fetchPolicy: 'cache-first', // 优先使用缓存数据
      errorPolicy: 'all',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  connectToDevTools: process.env.NODE_ENV === 'development', // 开发环境下连接DevTools
});

export default client; 