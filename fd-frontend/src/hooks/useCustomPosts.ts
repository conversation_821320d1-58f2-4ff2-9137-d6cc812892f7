import { useQuery, gql } from '@apollo/client';
import { Post } from '../types/post'; // 可以复用Post类型或创建一个更通用的ContentNode类型
import { useMemo } from 'react';
import { useSettings } from '@/contexts/SettingsContext';

// 硬编码的静态查询已被移除，将由外部动态传入

interface ContentNode {
  // 定义一个基础的、兼容多种类型的接口
  id: string;
  title?: string;
  slug?: string;
  date?: string;
  excerpt?: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
  [key: string]: any; // 允许其他任何字段
}

interface ContentNodesData {
  contentNodes: {
    nodes: ContentNode[];
    pageInfo?: {
      hasNextPage: boolean;
      endCursor: string;
    };
  };
}

interface UseCustomPostsOptions {
  first?: number;
  after?: string;
  postType: string;
  query: string; // 新增：接收动态生成的查询字符串
  categorySlug?: string;
}

/**
 * 获取自定义类型文章列表的Hook (动态查询版)
 * @param options 查询选项，必须包含动态生成的query
 * @returns 文章列表数据、加载状态和错误信息
 */
export const useCustomPosts = (options: UseCustomPostsOptions) => {
  const { postsPerPage } = useSettings();
  const { first: firstOption, after, postType, query, categorySlug } = options;
  const first = firstOption ?? postsPerPage;

  // 将postType字符串转换为GraphQL枚举需要的格式（例如 'book' -> 'BOOK')
  const contentTypes = postType ? [postType.toUpperCase()] : [];
  
  /**
   * 根据是否携带 categorySlug 动态生成 Query：
   *  - 无分类：使用原始 query 字符串
   *  - 有分类：在 where 中插入 taxQuery，并使用变量 $cats 传递 slug 数组
   * Apollo 的缓存 key 会把变量一起计算，这样不同 slug 会各自拥有独立缓存
   */
  const GQL_QUERY = useMemo(() => {
    if (!query) return null;

    if (!categorySlug) {
      // 无分类过滤，直接解析原始查询
      try {
        return gql(query);
      } catch (error) {
        console.error('[useCustomPosts] Query parsing error (noCat):', error);
        return null;
      }
    }

    const taxEnum = `${postType.toUpperCase()}CATEGORY`;

    // 1) 插入 $cats 变量定义
    let finalQuery = query.replace(
      /query\s+([A-Za-z0-9_]+)\s*\(([\s\S]*?)\)/,
      (match, opName, vars) => {
        const trimmedVars = vars.trim();
        if (/\$cats\s*:/.test(trimmedVars)) return match; // 已存在，不处理
        const newVars = trimmedVars ? `$cats: [String!], ${trimmedVars}` : '$cats: [String!]';
        return `query ${opName}(${newVars})`;
      }
    );

    // 2) 注入 taxQuery 过滤
    finalQuery = finalQuery.replace(/where:\s*\{([\s\S]*?)\}/, (match, content) => {
      if (/taxQuery:/.test(content)) return match; // 已存在，不处理
      const injected = `${content.trim()}, taxQuery: { taxArray: [{ taxonomy: ${taxEnum}, field: SLUG, terms: $cats, operator: IN }] }`;
      return `where: {${injected}}`;
    });

    console.log("[useCustomPosts] Final GraphQL Query:", finalQuery); // DEBUG: 打印最终查询语句
 
    try {
      return gql(finalQuery);
    } catch (error) {
      console.error('[useCustomPosts] Query parsing error (withCat):', error);
      return null;
    }
  }, [query, categorySlug, postType]);

  const variables = useMemo(() => {
    const vars: Record<string, any> = { first, after };
    if (categorySlug) {
      vars.cats = [categorySlug];
    }
    return vars;
  }, [first, after, categorySlug]);

  console.log('[useCustomPosts] Query Variables:', variables); // DEBUG: 打印变量

  const { data, loading, error, fetchMore, refetch } = useQuery<ContentNodesData>(GQL_QUERY!, {
    variables,
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'cache-and-network', // 恢复为缓存优先，网络同步策略
    skip: !postType || !GQL_QUERY,
  });

  if (error) {
    console.error('[useCustomPosts] GraphQL Error:', error);
  }

  const loadMore = (afterCursor: string) => {
    const newVariables = { ...variables, after: afterCursor };
    return fetchMore({ variables: newVariables });
  };

  return {
    posts: data?.contentNodes?.nodes || [],
    pageInfo: data?.contentNodes?.pageInfo,
    loading,
    error,
    loadMore,
    refetch,
  };
};
