# CPT分类筛选问题修复方案

## 🔍 问题分析

### 问题现象
在CPT列表页点击分类筛选时，新的分类文章会追加到旧文章后面，而不是替换显示。

### 日志分析
```
[CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12  // 初始全部文章
[CPL Click] Clicked category button: "knowledge"                          // 点击knowledge分类
[CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 13  // 变成13篇（12+1）
[CPL Click] Clicked category button: "tips"                               // 点击tips分类  
[CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 14  // 变成14篇（13+1）
```

### 根本原因
Apollo Client的`contentNodes`缓存策略存在问题：

1. **缓存键不够精确**：`keyArgs: ['where', ['contentTypes']]`没有包含`taxQuery`
2. **合并策略错误**：总是将新数据追加到旧数据，没有区分分页和筛选

## 🔧 修复方案

### 修改Apollo Client缓存配置

**文件**：`fd-frontend/src/lib/apollo-client.ts`

**修改前**：
```typescript
contentNodes: {
  keyArgs: ['where', ['contentTypes']],
  merge(existing, incoming) {
    if (!incoming) return existing;
    if (!existing) return incoming;
    
    const existingNodes = existing.nodes || [];
    const incomingNodes = incoming.nodes || [];

    return {
      ...incoming,
      nodes: [...existingNodes, ...incomingNodes], // 总是追加
    };
  },
},
```

**修改后**：
```typescript
contentNodes: {
  // 精确的缓存键：包含分类筛选信息，确保不同分类有独立缓存
  keyArgs: ['where', ['contentTypes', 'taxQuery']],
  merge(existing = { nodes: [] }, incoming, { args }) {
    if (!incoming) return existing;
    if (!existing) return incoming;
    
    // 判断是否是分页加载（有after参数）
    const isPagination = args?.after;
    
    // 如果不是分页加载，直接返回新数据（替换旧数据）
    if (!isPagination) {
      return incoming;
    }
    
    // 分页加载时，合并且去重
    const existingIds = new Set(existing.nodes.map((node: Node) => node.id));
    const uniqueNewNodes = incoming.nodes.filter((node: Node) => !existingIds.has(node.id));
    
    return {
      ...incoming,
      nodes: [...existing.nodes, ...uniqueNewNodes],
    };
  },
},
```

## 🎯 修复效果

### 缓存键优化
- **修改前**：不同分类的查询共享同一个缓存键
- **修改后**：每个分类筛选都有独立的缓存键

### 数据合并逻辑
- **修改前**：总是追加新数据到旧数据
- **修改后**：区分分页和筛选，筛选时替换数据，分页时合并数据

### 预期行为
1. 点击"全部"：显示所有文章
2. 点击"knowledge"：只显示knowledge分类的文章（替换，不追加）
3. 点击"tips"：只显示tips分类的文章（替换，不追加）
4. 分页加载：正确追加更多文章

## 🔒 影响评估

### 不受影响的功能
- **搜索功能**：使用不同的查询参数，有独立缓存
- **单个内容获取**：不涉及列表合并
- **其他CPT列表**：受益于更精确的缓存控制

### 受益的功能
- **所有CPT列表页**：分类筛选更准确
- **分页功能**：保持正常工作
- **缓存性能**：更精确的缓存键提高命中率

## 🧪 测试验证

### 测试步骤
1. 打开CPT列表页（如/post-type/note）
2. 观察初始加载的文章数量
3. 点击"knowledge"分类按钮
4. 验证只显示knowledge分类的文章
5. 点击"tips"分类按钮
6. 验证只显示tips分类的文章
7. 点击"全部"按钮
8. 验证显示所有文章

### 预期结果
- 分类切换时文章数量应该重置，不应该累加
- 每个分类显示的文章应该只属于该分类
- 分页功能应该正常工作

## 📝 技术说明

### 缓存键机制
Apollo Client使用`keyArgs`来确定查询的缓存键。通过添加`taxQuery`到缓存键中，确保：
- 无分类筛选的查询：`contentNodes({"where":{"contentTypes":["NOTE"]}})`
- knowledge分类筛选：`contentNodes({"where":{"contentTypes":["NOTE"],"taxQuery":{...}}})`
- tips分类筛选：`contentNodes({"where":{"contentTypes":["NOTE"],"taxQuery":{...}}})`

### 合并策略
通过检查`args?.after`参数来区分：
- **首次查询/筛选**：`after`为空，直接替换数据
- **分页加载**：`after`有值，合并数据并去重

这个修复方案是手术刀般精准的，只影响有问题的缓存行为，不会破坏其他功能。
