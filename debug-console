layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-5299d65464039579.js:1 [WS DEBUG] Socket available: false
layout-5299d65464039579.js:1 [WS DEBUG] Current pathname: /
layout-5299d65464039579.js:1 [WS DEBUG] Route prefixes: Object
layout-5299d65464039579.js:1 [WS DEBUG] No socket available, returning early
7269-0fa07f0679a526ba.js:1 初始化: 未找到认证令牌
layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-5299d65464039579.js:1 [WS DEBUG] Socket available: false
layout-5299d65464039579.js:1 [WS DEBUG] Current pathname: /
layout-5299d65464039579.js:1 [WS DEBUG] Route prefixes: Object
layout-5299d65464039579.js:1 [WS DEBUG] No socket available, returning early
layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-5299d65464039579.js:1 [WS DEBUG] Socket available: true
layout-5299d65464039579.js:1 [WS DEBUG] Current pathname: /
layout-5299d65464039579.js:1 [WS DEBUG] Route prefixes: Object
layout-5299d65464039579.js:1 [WS DEBUG] Socket connection state: true
layout-5299d65464039579.js:1 [WS DEBUG] Socket ID: d6p9s7Y_8ZeCtCY1AAAD
layout-5299d65464039579.js:1 [WS DEBUG] Registering event listeners...
layout-5299d65464039579.js:1 [WS DEBUG] Event listeners registered: Array(17)
layout-5299d65464039579.js:1 [WS DEBUG] Cleaning up event listeners...
layout-5299d65464039579.js:1 [WS DEBUG] Event listeners cleaned up
layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect END ==========
layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-5299d65464039579.js:1 [WS DEBUG] Socket available: true
layout-5299d65464039579.js:1 [WS DEBUG] Current pathname: /
layout-5299d65464039579.js:1 [WS DEBUG] Route prefixes: Object
layout-5299d65464039579.js:1 [WS DEBUG] Socket connection state: true
layout-5299d65464039579.js:1 [WS DEBUG] Socket ID: d6p9s7Y_8ZeCtCY1AAAD
layout-5299d65464039579.js:1 [WS DEBUG] Registering event listeners...
layout-5299d65464039579.js:1 [WS DEBUG] Event listeners registered: Array(17)
help?_rsc=1tyyj:1  Failed to load resource: the server responded with a status of 404 (Not Found)
faq?_rsc=1tyyj:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api-docs?_rsc=1tyyj:1  Failed to load resource: the server responded with a status of 404 (Not Found)
feedback?_rsc=1tyyj:1  Failed to load resource: the server responded with a status of 404 (Not Found)
sitemap?_rsc=1tyyj:1  Failed to load resource: the server responded with a status of 404 (Not Found)
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: undefinedhasNextPage: undefinedpageInfo: undefinedpostsCount: 0variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 0
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: initialPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
layout-5299d65464039579.js:1 [WS DEBUG] Cleaning up event listeners...
layout-5299d65464039579.js:1 [WS DEBUG] Event listeners cleaned up
layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect END ==========
layout-5299d65464039579.js:1 [WS DEBUG] ========== WebSocketEventHub useEffect START ==========
layout-5299d65464039579.js:1 [WS DEBUG] Socket available: true
layout-5299d65464039579.js:1 [WS DEBUG] Current pathname: /note
layout-5299d65464039579.js:1 [WS DEBUG] Route prefixes: Object
layout-5299d65464039579.js:1 [WS DEBUG] Socket connection state: true
layout-5299d65464039579.js:1 [WS DEBUG] Socket ID: d6p9s7Y_8ZeCtCY1AAAD
layout-5299d65464039579.js:1 [WS DEBUG] Registering event listeners...
layout-5299d65464039579.js:1 [WS DEBUG] Event listeners registered: Array(17)
8742-db7a135ca3b44b5f.js:1 [CPL Effect] activeCategory changed to "". Updating URL.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truepageInfo: {__typename: 'RootQueryToContentNodeConnectionPageInfo', hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjcz'}postsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg5"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg5"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg5"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg5"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg5"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg5"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjIx"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNTM2"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNTM2"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNTM2"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNTM2"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNTM2"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: ObjectactiveCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNTM2"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: Objectafter: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: ObjectcategorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNDc3"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNDc3"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: after: undefinedfirst: 12[[Prototype]]: Object[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined}after: undefinedfirst: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 12, pageInfo: {…}, hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNDc3', categorySlug: undefined, …}categorySlug: undefinedendCursor: "YXJyYXljb25uZWN0aW9uOjIyNDc3"hasNextPage: truepageInfo: {__typename: 'RootQueryToContentNodeConnectionPageInfo', hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNDc3'}postsCount: 12variables: {first: 12, after: undefined}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNDc3', source: 'apollo', activeCategory: ''}activeCategory: ""endCursor: "YXJyYXljb25uZWN0aW9uOjIyNDc3"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: ""
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined}
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 12, pageInfo: {…}, hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNDU2', categorySlug: undefined, …}
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNDU2', source: 'apollo', activeCategory: ''}
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Click] Clicked category button: "knowledge"
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: "knowledge"
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Final GraphQL Query: 
        fragment NoteListFields on Note {
  id
  databaseId
  title
  slug
  shortUuid
  date
  excerpt(format: RENDERED)
  featuredImage {
    node {
      sourceUrl
      altText
    }
  }
  noteFields {
        noteSource
  }
}


        query GetCptList($cats: [String!], $first: Int, $after: String) {
          contentNodes(
            first: $first,
            after: $after,
            where: {contentTypes: [NOTE], taxQuery: { taxArray: [{ taxonomy: NOTECATEGORY, field: SLUG, terms: $cats, operator: IN }] }}
          ) {
            nodes {
              ...NoteListFields
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined, cats: Array(1)}after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 0, pageInfo: undefined, hasNextPage: undefined, endCursor: undefined, categorySlug: 'knowledge', …}
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 0
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 0. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjcz', source: 'initial', activeCategory: 'knowledge'}activeCategory: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truesource: "initial"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: "knowledge"
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined, cats: Array(1)}after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 0, pageInfo: undefined, hasNextPage: undefined, endCursor: undefined, categorySlug: 'knowledge', …}categorySlug: "knowledge"endCursor: undefinedhasNextPage: undefinedpageInfo: undefinedpostsCount: 0variables: after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 0
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 0. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjcz', source: 'initial', activeCategory: 'knowledge'}activeCategory: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truesource: "initial"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Effect] activeCategory changed to "knowledge". Updating URL.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: "knowledge"
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined, cats: Array(1)}after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 0, pageInfo: undefined, hasNextPage: undefined, endCursor: undefined, categorySlug: 'knowledge', …}categorySlug: "knowledge"endCursor: undefinedhasNextPage: undefinedpageInfo: undefinedpostsCount: 0variables: after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 0
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 0. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjcz', source: 'initial', activeCategory: 'knowledge'}activeCategory: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjcz"hasNextPage: truesource: "initial"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Effect] activeCategory changed to "knowledge". Updating URL.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: "knowledge"
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined, cats: Array(1)}after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 12, pageInfo: {…}, hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjc4', categorySlug: 'knowledge', …}categorySlug: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjc4"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjc4"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined, cats: Array(1)}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjc4', source: 'apollo', activeCategory: 'knowledge'}activeCategory: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjc4"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: "knowledge"
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined, cats: Array(1)}
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 12, pageInfo: {…}, hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjc4', categorySlug: 'knowledge', …}categorySlug: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjc4"hasNextPage: truepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjc4"hasNextPage: true__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined, cats: Array(1)}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=true, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: true, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjc4', source: 'apollo', activeCategory: 'knowledge'}activeCategory: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjc4"hasNextPage: truesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
8742-db7a135ca3b44b5f.js:1 [CPL Render] Component rendered. postType: note, initialPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL State] activeCategory is: "knowledge"
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Query Variables: {first: 12, after: undefined, cats: Array(1)}after: undefinedcats: Array(1)0: "knowledge"length: 1[[Prototype]]: Array(0)first: 12[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [useCustomPosts] Return data: {postsCount: 12, pageInfo: {…}, hasNextPage: false, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjg2', categorySlug: 'knowledge', …}categorySlug: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg2"hasNextPage: falsepageInfo: endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg2"hasNextPage: false__typename: "RootQueryToContentNodeConnectionPageInfo"[[Prototype]]: ObjectpostsCount: 12variables: {first: 12, after: undefined, cats: Array(1)}[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL Hook] useCustomPosts returned: loading=false, apolloPosts count: 12
8742-db7a135ca3b44b5f.js:1 [CPL Logic] Final displayPosts count: 12. Source: apolloPosts
8742-db7a135ca3b44b5f.js:1 [CPL PageInfo] displayPageInfo: {hasNextPage: false, endCursor: 'YXJyYXljb25uZWN0aW9uOjIyNjg2', source: 'apollo', activeCategory: 'knowledge'}activeCategory: "knowledge"endCursor: "YXJyYXljb25uZWN0aW9uOjIyNjg2"hasNextPage: falsesource: "apollo"[[Prototype]]: Object
8742-db7a135ca3b44b5f.js:1 [CPL RenderView] Rendering view component with 12 posts.
